import type { UserRole } from '@/types/auth';
import type { ProjectRole } from '@/features/project-invitations/types/invitation';

/**
 * Maps user roles to appropriate project roles for project creation.
 * This is different from invitation role mapping - this determines what role
 * a user should have when they CREATE a project vs when they're INVITED to one.
 * 
 * Project Creation Role Assignment:
 * - contractor → competent_person (contractors who create projects are typically competent persons)
 * - admin → admin (admins remain admins in projects they're added to)
 * - viewer → viewer (viewers remain viewers)
 */
export function mapUserRoleToProjectRole(userRole: UserRole): ProjectRole {
  switch (userRole) {
    case 'contractor':
      // Contractors who create projects are typically competent persons
      // They have the technical expertise to manage the project
      return 'competent_person';
    case 'admin':
      // Admins (JKR officials) maintain admin role in projects
      return 'admin';
    case 'viewer':
      // Viewers remain viewers
      return 'viewer';
    default:
      // Fallback to viewer for any unexpected role
      return 'viewer';
  }
}

/**
 * Determines if a user should have project admin privileges based on context.
 * This is used for special cases where a contractor might need admin privileges.
 * 
 * @param userRole - The user's global role
 * @param isProjectCreator - Whether the user is creating the project
 * @param hasAdminAccessMode - Whether the user has project-level admin access mode
 * @returns The appropriate project role considering admin privileges
 */
export function getProjectRoleWithAdminContext(
  userRole: UserRole,
  isProjectCreator: boolean = false,
  hasAdminAccessMode: boolean = false
): ProjectRole {
  // If user is a contractor with project-level admin access mode, they can be project admin
  if (userRole === 'contractor' && hasAdminAccessMode) {
    return 'admin';
  }
  
  // If user is an admin, they get admin role
  if (userRole === 'admin') {
    return 'admin';
  }
  
  // For project creators who are contractors, default to competent_person
  if (isProjectCreator && userRole === 'contractor') {
    return 'competent_person';
  }
  
  // Use standard mapping for other cases
  return mapUserRoleToProjectRole(userRole);
}

/**
 * Validates if a user role can be assigned a specific project role.
 * This helps prevent invalid role assignments.
 */
export function canUserHaveProjectRole(userRole: UserRole, projectRole: ProjectRole): boolean {
  switch (userRole) {
    case 'contractor':
      // Contractors can be technicians, competent persons, or admins (if they have admin access mode)
      return ['technician', 'competent_person', 'admin'].includes(projectRole);
    case 'admin':
      // Admins can have any project role, but typically admin or viewer
      return ['admin', 'viewer', 'competent_person', 'technician'].includes(projectRole);
    case 'viewer':
      // Viewers can only be viewers or potentially technicians
      return ['viewer', 'technician'].includes(projectRole);
    default:
      return false;
  }
}

/**
 * Gets a human-readable description of why a specific project role is assigned.
 */
export function getProjectRoleAssignmentReason(
  userRole: UserRole, 
  projectRole: ProjectRole, 
  isProjectCreator: boolean = false
): string {
  if (isProjectCreator && userRole === 'contractor' && projectRole === 'competent_person') {
    return 'Project creator with contractor role assigned as competent person';
  }
  
  if (userRole === 'admin' && projectRole === 'admin') {
    return 'Admin user maintains administrative privileges in project';
  }
  
  if (userRole === 'contractor' && projectRole === 'admin') {
    return 'Contractor with project-level admin access mode';
  }
  
  return `Standard role mapping: ${userRole} → ${projectRole}`;
}
